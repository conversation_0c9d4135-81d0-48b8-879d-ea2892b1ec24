import backtrader as bt

class BaseStrategy(bt.Strategy):
    """策略基类"""
    params = (
        ('printlog', True),
    )
    
    def log(self, txt, dt=None):
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print('%s, %s' % (dt.isoformat(), txt))

class MA20Strategy(BaseStrategy):
    """20日均线策略"""
    params = (('ma_period', 20),)
    
    def __init__(self):
        self.mas = {}
        self.crossovers = {}
        self.orders = {}
        
        for i, data in enumerate(self.datas):
            self.mas[data] = bt.indicators.SimpleMovingAverage(
                data.close, period=self.params.ma_period)
            self.crossovers[data] = bt.indicators.CrossOver(
                data.close, self.mas[data])
            self.orders[data] = None

class MACDStrategy(BaseStrategy):
    """MACD策略"""
    def __init__(self):
        self.macds = {}
        self.orders = {}
        
        for data in self.datas:
            self.macds[data] = bt.indicators.MACD(data.close)
            self.orders[data] = None

class RSIStrategy(BaseStrategy):
    """RSI策略"""
    params = (('rsi_period', 14), ('rsi_upper', 70), ('rsi_lower', 30))
    
    def __init__(self):
        self.rsis = {}
        self.orders = {}
        
        for data in self.datas:
            self.rsis[data] = bt.indicators.RSI(
                data.close, period=self.params.rsi_period)
            self.orders[data] = None

class MACDBollVolumeStrategy(BaseStrategy):
    """MACD金叉+布林带+成交量综合策略"""
    params = (
        ('macd_fast', 12),
        ('macd_slow', 26), 
        ('macd_signal', 9),
        ('boll_period', 20),
        ('boll_dev', 2),
        ('volume_ma_period', 20),
        ('high_volume_ratio', 2.0),  # 巨量成交量倍数
        ('high_turnover_threshold', 0.05),  # 高换手率阈值5%
        ('big_candle_ratio', 0.03),  # 大阳线/大阴线涨跌幅阈值3%
        ('upper_shadow_ratio', 0.02),  # 长上影线比例阈值2%
        ('lookback_days', 20),  # MACD金叉回看天数
        ('buy_wait_days', 3),  # 买入等待天数
    )
    
    def __init__(self):
        self.macds = {}
        self.bolls = {}
        self.volume_mas = {}
        self.orders = {}
        self.golden_cross_dates = {}  # 记录金叉日期
        self.big_candle_after_cross = {}  # 记录金叉后大阳线
        self.price_near_upper = {}  # 记录价格接近上轨的日期
        
        for data in self.datas:
            # MACD指标
            self.macds[data] = bt.indicators.MACD(
                data.close,
                period_me1=self.params.macd_fast,
                period_me2=self.params.macd_slow,
                period_signal=self.params.macd_signal
            )
            
            # 布林带指标
            self.bolls[data] = bt.indicators.BollingerBands(
                data.close,
                period=self.params.boll_period,
                devfactor=self.params.boll_dev
            )
            
            # 成交量均线
            self.volume_mas[data] = bt.indicators.SimpleMovingAverage(
                data.volume, period=self.params.volume_ma_period
            )
            
            self.orders[data] = None
            self.golden_cross_dates[data] = []
            self.big_candle_after_cross[data] = False
            self.price_near_upper[data] = None
    
    def is_macd_golden_cross(self, data):
        """检测MACD金叉"""
        macd = self.macds[data]
        if len(macd.macd) < 2:
            return False
        return (macd.macd[0] > macd.signal[0] and 
                macd.macd[-1] <= macd.signal[-1] and
                macd.macd[0] < 0)  # 低位金叉
    
    def is_big_yang_candle(self, data):
        """检测大阳线"""
        change_ratio = (data.close[0] - data.open[0]) / data.open[0]
        return change_ratio >= self.params.big_candle_ratio
    
    def is_big_yin_candle(self, data):
        """检测大阴线"""
        change_ratio = (data.open[0] - data.close[0]) / data.open[0]
        return change_ratio >= self.params.big_candle_ratio
    
    def has_long_upper_shadow(self, data):
        """检测长上影线"""
        body_size = abs(data.close[0] - data.open[0])
        upper_shadow = data.high[0] - max(data.close[0], data.open[0])
        if body_size == 0:
            return False
        shadow_ratio = upper_shadow / body_size
        return shadow_ratio >= self.params.upper_shadow_ratio
    
    def is_high_volume(self, data):
        """检测巨量成交"""
        if len(self.volume_mas[data]) == 0:
            return False
        return data.volume[0] >= self.volume_mas[data][0] * self.params.high_volume_ratio
    
    def is_high_turnover(self, data):
        """检测高换手率（需要流通股本数据，这里简化处理）"""
        # 简化：用成交量/成交额比例估算
        if data.volume[0] == 0:
            return False
        # 这里需要根据实际数据结构调整
        return self.is_high_volume(data)  # 简化为高成交量
    
    def is_price_between_mid_upper(self, data):
        """检测股价是否在布林带中轨与上轨之间"""
        boll = self.bolls[data]
        return boll.mid[0] <= data.close[0] <= boll.top[0]
    
    def is_price_near_mid_from_upper(self, data):
        """检测股价是否从上轨下降接近中轨"""
        boll = self.bolls[data]
        if len(data.close) < 3:
            return False
        
        # 检查是否从接近上轨下降到接近中轨
        distance_to_mid = abs(data.close[0] - boll.mid[0])
        band_width = boll.top[0] - boll.mid[0]
        
        return distance_to_mid <= band_width * 0.2  # 接近中轨（20%范围内）
    
    def check_macd_uptrend(self, data):
        """检查MACD是否保持向上趋势"""
        macd = self.macds[data]
        if len(macd.macd) < 3:
            return False
        return macd.macd[0] > macd.macd[-1] > macd.macd[-2]
    
    def next(self):
        for data in self.datas:
            # 检测MACD金叉
            if self.is_macd_golden_cross(data):
                current_date = self.datas[0].datetime.date(0)
                self.golden_cross_dates[data].append(current_date)
                self.big_candle_after_cross[data] = False
            
            # 清理过期的金叉记录
            current_date = self.datas[0].datetime.date(0)
            self.golden_cross_dates[data] = [
                date for date in self.golden_cross_dates[data]
                if (current_date - date).days <= self.params.lookback_days
            ]
            
            # 检测金叉后的大阳线+高成交量
            if (self.golden_cross_dates[data] and 
                not self.big_candle_after_cross[data] and
                self.is_big_yang_candle(data) and
                (self.is_high_volume(data) or self.is_high_turnover(data))):
                self.big_candle_after_cross[data] = True
            
            # 记录价格接近上轨的时间
            if self.is_price_between_mid_upper(data):
                boll = self.bolls[data]
                distance_to_upper = abs(data.close[0] - boll.top[0])
                band_width = boll.top[0] - boll.mid[0]
                if distance_to_upper <= band_width * 0.1:  # 接近上轨
                    self.price_near_upper[data] = current_date
            
            # 买入条件检查
            if (not self.getposition(data) and 
                self.orders[data] is None and
                self.golden_cross_dates[data] and  # 20天内有金叉
                self.big_candle_after_cross[data] and  # 金叉后有大阳线+高成交量
                self.check_macd_uptrend(data) and  # MACD保持向上
                self.is_price_between_mid_upper(data) and  # 价格在中轨与上轨间
                self.price_near_upper[data] and  # 曾经接近过上轨
                (current_date - self.price_near_upper[data]).days <= self.params.buy_wait_days and  # 3天内
                self.is_price_near_mid_from_upper(data)):  # 现在接近中轨
                
                self.log(f'买入信号: {data._name}, 价格: {data.close[0]:.2f}')
                self.orders[data] = self.buy(data=data)
            
            # 卖出条件检查
            elif (self.getposition(data) and 
                  self.orders[data] is None):
                
                # 检查是否创新高
                is_new_high = data.close[0] >= max(data.close.get(size=20))
                
                # 卖出条件：新高+高成交量+长上影线或大阴线
                if (is_new_high and
                    (self.is_high_volume(data) or self.is_high_turnover(data)) and
                    (self.has_long_upper_shadow(data) or self.is_big_yin_candle(data))):
                    
                    self.log(f'卖出信号: {data._name}, 价格: {data.close[0]:.2f}')
                    self.orders[data] = self.sell(data=data)
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Completed]:
            data = order.data
            if order.isbuy():
                self.log(f'买入执行: {data._name}, 价格: {order.executed.price:.2f}')
            elif order.issell():
                self.log(f'卖出执行: {data._name}, 价格: {order.executed.price:.2f}')
                # 重置状态
                self.big_candle_after_cross[data] = False
                self.price_near_upper[data] = None
            
            self.orders[data] = None
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.orders[order.data] = None

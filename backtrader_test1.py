import akshare as ak
import backtrader as bt
import datetime
import pandas as pd

# 添加印花税佣金方案类
class stampDutyCommissionScheme(bt.CommInfoBase):
    '''
    定义一个印花税、佣金和过户费方案
    '''
    params = (
        ('commission', 0.0001),  # 佣金率
        ('stamp_duty', 0.0005),   # 印花税率
        ('transfer_fee', 0.00002),  # 过户费率（上海市场，万分之0.2）
        ('market', 'sz'),  # 市场，'sh'表示上海，'sz'表示深圳
        ('stocklike', True),
        ('commtype', bt.CommInfoBase.COMM_PERC),  # 按百分比收取佣金
    )

    def _getcommission(self, size, price, pseudoexec):
        '''
        计算佣金，买入时只收取佣金和过户费（上海市场），卖出时收取佣金、印花税和过户费（上海市场）
        '''
        commission = abs(size) * price * self.p.commission
        
        # 卖出时收取印花税
        if size < 0:  
            commission += abs(size) * price * self.p.stamp_duty
        
        # 上海市场收取过户费（买入和卖出都收）
        if self.p.market.lower() == 'sh':
            commission += abs(size) * price * self.p.transfer_fee
        # 设置最低佣金为5元
        if commission < 5.0:
            commission = 5.0  
              
        return commission

class MA20Strategy(bt.Strategy):    
    params = (        
        ('ma_period', 20),    
        )
    def log(self, txt, dt=None):
        '''可选，构建策略打印日志的函数：可用于打印订单记录或交易记录等'''
        dt = dt or self.datas[0].datetime.date(0)
        print('%s, %s' % (dt.isoformat(), txt))

    def __init__(self):
        # 保存对收盘价线的引用
        self.dataclose = self.datas[0].close
        # 移动平均线指标        
        self.ma = bt.indicators.SimpleMovingAverage(self.data.close, period=self.params.ma_period)        
        self.crossover = bt.indicators.CrossOver(self.data.close, self.ma)
        # 跟踪挂单
        self.order = None

    def notify_order(self, order):
        '''可选，打印订单信息'''
        if order.status in [order.Submitted, order.Accepted]:
           # Buy/Sell order submitted/accepted to/by broker - Nothing to do
            return
       # Check if an order has been completed
       # Attention: broker could reject order if not enough cash
        if order.status in [order.Completed]:
            if order.isbuy():
               self.log(
                   'BUY EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %
                  (order.executed.price,
                    order.executed.value,
                    order.executed.comm))
               self.buyprice = order.executed.price
               self.buycomm = order.executed.comm
            elif order.issell():
              self.log('SELL EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %
                        (order.executed.price,
                         order.executed.value,
                         order.executed.comm))
            self.bar_executed = len(self)
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')
       # Write down: no pending order
        self.order = None

    def notify_trade(self, trade):
        '''可选，打印交易信息'''
        if not trade.isclosed:
            return
       # 每笔交易收益 毛利和净利
        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' %
            (trade.pnl, trade.pnlcomm))


    def next(self):
         # Simply log the closing price of the series from the reference
        self.log('Close, %.2f' % self.dataclose[0])
       # Check if an order is pending ... if yes, we cannot send a 2nd one
        if self.order:
           return
        
        if not self.position:            
            # 修改买入条件：使用 crossover 信号，当收盘价从下方穿过 MA 时买入
            if self.crossover > 0:  # crossover > 0 表示向上穿过
                self.log('BUY CREATE, %.2f' % self.dataclose[0])               
                self.order = self.buy()      
        # 修改卖出条件：使用 crossover 信号，当收盘价从上方穿过 MA 时卖出
        elif self.crossover < 0:  # crossover < 0 表示向下穿过
            self.log('SELL CREATE, %.2f' % self.dataclose[0])            
            self.order = self.sell()

class PortfolioManager(bt.Strategy):
    """投资组合管理器"""
    params = (
        ('rebalance_freq', 20),  # 重平衡频率
        ('max_positions', 5),    # 最大持仓数量
        ('position_size', 0.2),  # 每个位置的资金比例
    )
    
    def __init__(self):
        self.rebalance_counter = 0
        self.stock_scores = {}
        # 预先计算所有指标
        self.rsis = {}
        self.mas = {}
        
        for data in self.datas:
            self.rsis[data] = bt.indicators.RSI(data.close, period=14)
            self.mas[data] = bt.indicators.SimpleMovingAverage(data.close, period=20)
        
    def calculate_stock_scores(self):
        """计算股票评分"""
        scores = {}
        for data in self.datas:
            # 检查指标是否有足够数据
            if len(self.mas[data]) == 0 or len(self.rsis[data]) == 0:
                scores[data] = 0
                continue
                
            score = 0
            try:
                if data.close[0] > self.mas[data][0]:
                    score += 1
                if 30 < self.rsis[data][0] < 70:
                    score += 1
            except IndexError:
                # 如果数据不足，给予默认分数
                score = 0
                
            scores[data] = score
        return scores
    
    def next(self):
        self.rebalance_counter += 1
        
        # 确保有足够的数据再开始重平衡
        if (self.rebalance_counter % self.params.rebalance_freq == 0 and 
            len(self) > 20):  # 等待20个交易日后再开始
            self.rebalance_portfolio()
    
    def rebalance_portfolio(self):
        """重平衡投资组合"""
        try:
            scores = self.calculate_stock_scores()
            sorted_stocks = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            
            # 卖出不在前N名的股票
            for data in self.datas:
                if self.getposition(data).size > 0:
                    if data not in [stock[0] for stock in sorted_stocks[:self.params.max_positions]]:
                        self.close(data=data)
            
            # 买入前N名股票
            target_value = self.broker.getvalue() * self.params.position_size
            for stock, score in sorted_stocks[:self.params.max_positions]:
                if self.getposition(stock).size == 0 and score > 0:
                    try:
                        size = int(target_value / stock.close[0] / 100) * 100
                        if size > 0:
                            self.buy(data=stock, size=size)
                    except (IndexError, ZeroDivisionError):
                        continue
        except Exception as e:
            print(f"重平衡时出错: {e}")

def get_multiple_stocks_data(stock_codes, start_date, end_date):
    """获取多只股票数据"""
    all_data = {}
    for code in stock_codes:
        try:
            df = ak.stock_zh_a_hist(symbol=code, start_date=start_date, end_date=end_date)
            if not df.empty:
                df = df.rename(columns={
                    '日期': 'date', '开盘': 'open', '收盘': 'close',
                    '最高': 'high', '最低': 'low', '成交量': 'volume'
                })
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                all_data[code] = bt.feeds.PandasData(dataname=df)
        except Exception as e:
            print(f"获取股票 {code} 数据失败: {e}")
    return all_data

def run_backtest(stock_code, start_date, end_date):    
    # 使用akshare获取股票数据    
    try:        
        df = ak.stock_zh_a_hist(symbol=stock_code, start_date=start_date, end_date=end_date)    
    except Exception as e:        
        print(f"获取股票数据时出错: {e}")        
        return

    if df.empty:        
        print("未获取到股票数据，请检查股票代码和日期范围。")        
        return
    # 重命名列以匹配backtrader的要求    
    df = df.rename(columns={        
        '日期': 'date', 
        '开盘': 'open', 
        '收盘': 'close',        
        '最高': 'high', 
        '最低': 'low', 
        '成交量': 'volume'    
        })
    # 将日期列转换为datetime类型    
    df['date'] = pd.to_datetime(df['date'])
    # 设置日期为索引    
    df.set_index('date', inplace=True)
    # 创建backtrader的数据源    
    data = bt.feeds.PandasData(dataname=df)
    # 初始化cerebro引擎    
    cerebro = bt.Cerebro()
    # 添加数据    
    cerebro.adddata(data)
    # 设置初始资金    
    cerebro.broker.setcash(100000.0)
    # 每笔交易使用固定交易量  
    cerebro.addsizer(bt.sizers.FixedSize, stake=100)
    # 设置佣金    
    # 判断市场类型（上海或深圳）
    market_type = 'sh' if stock_code.startswith('6') else 'sz'
    comminfo = stampDutyCommissionScheme(
        stamp_duty=0.0005,
        commission=0.00012,
        transfer_fee=0.00002,
        market=market_type
    )
    cerebro.broker.addcommissioninfo(comminfo)
    # 设置滑点：双边各0.0001
    cerebro.broker.set_slippage_perc(perc=0.0001)
    # 添加策略    
    cerebro.addstrategy(MA20Strategy)
    # 添加分析器    
    cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio')    
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')    
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    # 添加观测器
    cerebro.addobserver(bt.observers.Broker)   #现金和价值
    cerebro.addobserver(bt.observers.Trades)   #交易
    cerebro.addobserver(bt.observers.BuySell)  #买卖下单
    # 运行回测    
    results = cerebro.run()
    # 打印结果    
    strat = results[0]    
    print(f"初始资金: {cerebro.broker.startingcash:.2f}")    
    print(f"最终资金: {cerebro.broker.getvalue():.2f}")
    returns = strat.analyzers.returns.get_analysis()    
    if 'rtot' in returns:        
        print(f"总回报率: {returns['rtot']:.2%}")    
    else:        
        print("无法计算总回报率")
    sharpe = strat.analyzers.sharpe_ratio.get_analysis()    
    if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:        
        print(f"年化夏普比率: {sharpe['sharperatio']:.2f}")    
    else:        
        print("无法计算夏普比率，可能是由于数据不足或者没有交易发生")
    drawdown = strat.analyzers.drawdown.get_analysis()    
    if 'max' in drawdown and 'drawdown' in drawdown['max']:        
        print(f"最大回撤: {drawdown['max']['drawdown']:.2%}")    
    else:        
        print("无法计算最大回撤")
    # 绘制结果    
    try:        
        cerebro.plot()    
    except Exception as e:        
        print(f"绘图时出错: {e}")

    # 添加错误处理，防止 quantstats 报告生成错误
    try:
        portfolio_stats = results[0].analyzers.getbyname('pyfolio')
        returns, positions, transactions, gross_lev = portfolio_stats.get_pf_items()
        returns.index = returns.index.tz_convert(None)
        
        try:
            import quantstats
            import numpy as np
            import warnings
            import os
            
            # 忽略 FutureWarning
            warnings.filterwarnings('ignore', category=FutureWarning)
            
            # 修复 numpy.product 不存在的问题
            if not hasattr(np, 'product'):
                np.product = np.prod
            
            # 使用绝对路径保存和打开报告
            report_path = os.path.join(os.getcwd(), 'stats.html')
            quantstats.reports.html(returns, output=report_path, title='Stock Sentiment')
            
            import webbrowser
            # 使用 file:// 协议和绝对路径确保文件能被正确打开
            webbrowser_url = 'file://' + os.path.abspath(report_path)
            print(f"尝试打开报告: {webbrowser_url}")
            
            # 尝试使用不同的浏览器打开
            try:
                # 先尝试默认浏览器
                if not webbrowser.open(webbrowser_url):
                    # 如果默认浏览器失败，尝试指定 Safari
                    webbrowser.get('safari').open(webbrowser_url)
            except Exception as browser_error:
                print(f"自动打开浏览器失败: {browser_error}")
                print(f"请手动打开报告文件: {report_path}")
                
            print("已生成详细的回测报告: stats.html")
        except ImportError:
            print("警告: 未安装 quantstats 库，无法生成详细报告")
            print("请使用 pip install quantstats 安装该库")
        except Exception as e:
            print(f"生成 quantstats 报告时出错: {e}")
    except Exception as e:
        print(f"处理回测结果时出错: {e}")
    
    # 修正拼写错误：Profolio -> Portfolio
    print('Final Portfolio Value : %.2f' %cerebro.broker.getvalue())

def run_portfolio_backtest(stock_codes, start_date, end_date, strategy_class, **strategy_params):
    """多股票组合回测"""
    # 获取多股票数据
    all_data = get_multiple_stocks_data(stock_codes, start_date, end_date)
    
    if not all_data:
        print("未获取到任何股票数据")
        return
    
    cerebro = bt.Cerebro()
    
    # 添加所有股票数据
    for code, data in all_data.items():
        cerebro.adddata(data, name=code)
    
    # 设置初始资金
    cerebro.broker.setcash(1000000.0)
    
    # 添加策略
    cerebro.addstrategy(strategy_class, **strategy_params)
    
    # 设置佣金（为每只股票设置）
    for code in stock_codes:
        market_type = 'sh' if code.startswith('6') else 'sz'
        comminfo = stampDutyCommissionScheme(
            stamp_duty=0.0005,
            commission=0.00012,
            transfer_fee=0.00002,
            market=market_type
        )
        cerebro.broker.addcommissioninfo(comminfo)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    # 运行回测
    results = cerebro.run()
    
    # 打印结果
    print_backtest_results(cerebro, results[0])
    
    return results

def print_backtest_results(cerebro, strat):
    """打印回测结果"""
    print(f"初始资金: {cerebro.broker.startingcash:.2f}")    
    print(f"最终资金: {cerebro.broker.getvalue():.2f}")
    
    # 计算总回报率
    total_return = (cerebro.broker.getvalue() - cerebro.broker.startingcash) / cerebro.broker.startingcash
    print(f"总回报率: {total_return:.2%}")
    
    # 获取分析器结果
    try:
        returns = strat.analyzers.returns.get_analysis()    
        if 'rtot' in returns:        
            print(f"分析器总回报率: {returns['rtot']:.2%}")    
    except:
        print("无法获取returns分析器结果")
    
    try:
        sharpe = strat.analyzers.sharpe.get_analysis()    
        if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:        
            print(f"年化夏普比率: {sharpe['sharperatio']:.2f}")    
        else:
            print("无法计算夏普比率")
    except:
        print("无法获取sharpe分析器结果")
    
    try:
        drawdown = strat.analyzers.drawdown.get_analysis()    
        if 'max' in drawdown and 'drawdown' in drawdown['max']:        
            print(f"最大回撤: {drawdown['max']['drawdown']:.2%}")
        else:
            print("无法计算最大回撤")
    except:
        print("无法获取drawdown分析器结果")

# 导入策略
from strategies import MA20Strategy, MACDBollVolumeStrategy, MACDStrategy, RSIStrategy

# 使用示例
if __name__ == "__main__":
    stock_codes = ["000001", "000002", "600000", "600036"]
    
    # 测试不同策略
    strategies = [
        (MA20Strategy, {'ma_period': 20}),
        (MACDBollVolumeStrategy, {
            'high_volume_ratio': 2.0,
            'big_candle_ratio': 0.03,
            'buy_wait_days': 3
        }),
        (PortfolioManager, {'rebalance_freq': 20, 'max_positions': 3}),
    ]
    
    for strategy_class, params in strategies:
        print(f"\n=== 测试策略: {strategy_class.__name__} ===")
        run_portfolio_backtest(stock_codes, "20230101", "20240920", strategy_class, **params)


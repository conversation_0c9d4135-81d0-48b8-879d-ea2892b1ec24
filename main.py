from backtrader_test1 import run_portfolio_backtest
from strategy_factory import StrategyFactory

def main():
    """主函数"""
    stock_codes = ["000001", "000002", "600000", "600036"]
    start_date = "20230101"
    end_date = "20240920"
    
    # 获取可用策略
    available_strategies = StrategyFactory.get_available_strategies()
    print(f"可用策略: {available_strategies}")
    
    # 测试不同策略
    strategy_configs = [
        ('ma20', {'ma_period': 20}),
        ('macd_boll_volume', {
            'high_volume_ratio': 2.0,
            'big_candle_ratio': 0.03,
            'buy_wait_days': 3
        }),
        ('portfolio', {
            'rebalance_freq': 20,
            'max_positions': 3
        })
    ]
    
    for strategy_name, params in strategy_configs:
        print(f"\n{'='*50}")
        print(f"测试策略: {strategy_name}")
        print(f"参数: {params}")
        print(f"{'='*50}")
        
        try:
            strategy_class, strategy_params = StrategyFactory.create_strategy(
                strategy_name, **params
            )
            run_portfolio_backtest(
                stock_codes, 
                start_date, 
                end_date, 
                strategy_class, 
                **strategy_params
            )
        except Exception as e:
            print(f"策略 {strategy_name} 运行失败: {e}")

if __name__ == "__main__":
    main()
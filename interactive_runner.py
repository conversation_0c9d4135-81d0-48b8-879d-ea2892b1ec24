from strategy_factory import StrategyFactory
from backtrader_test1 import run_portfolio_backtest

def interactive_backtest():
    """交互式回测"""
    print("=== 股票回测系统 ===")
    
    # 输入股票代码
    stock_input = input("请输入股票代码（用逗号分隔，如：000001,000002）: ")
    stock_codes = [code.strip() for code in stock_input.split(',')]
    
    # 输入日期范围
    start_date = input("请输入开始日期（格式：20230101）: ")
    end_date = input("请输入结束日期（格式：20240920）: ")
    
    # 选择策略
    strategies = StrategyFactory.get_available_strategies()
    print(f"\n可用策略: {', '.join(strategies)}")
    strategy_name = input("请选择策略: ")
    
    if strategy_name not in strategies:
        print("无效策略！")
        return
    
    # 运行回测
    try:
        strategy_class, params = StrategyFactory.create_strategy(strategy_name)
        run_portfolio_backtest(stock_codes, start_date, end_date, strategy_class, **params)
    except Exception as e:
        print(f"回测失败: {e}")

if __name__ == "__main__":
    interactive_backtest()
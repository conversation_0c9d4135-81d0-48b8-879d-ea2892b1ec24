import akshare as ak
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
import threading
import schedule

class DataCollector:
    def __init__(self, db_path="stock_data.db"):
        self.db_path = db_path
        
    def get_all_stock_codes(self):
        """获取所有股票代码"""
        try:
            # 获取A股股票列表
            stock_info = ak.stock_info_a_code_name()
            return stock_info['code'].tolist()
        except Exception as e:
            print(f"获取股票代码失败: {e}")
            return []
    
    def update_stock_info(self):
        """更新股票基本信息"""
        try:
            stock_info = ak.stock_info_a_code_name()
            conn = sqlite3.connect(self.db_path)
            
            for _, row in stock_info.iterrows():
                try:
                    # 获取详细信息
                    detail = ak.stock_individual_info_em(symbol=row['code'])
                    
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT OR REPLACE INTO stock_info 
                        (code, name, market, updated_at) 
                        VALUES (?, ?, ?, ?)
                    ''', (row['code'], row['name'], 
                         'sh' if row['code'].startswith('6') else 'sz',
                         datetime.now()))
                    
                    time.sleep(0.1)  # 避免请求过快
                except Exception as e:
                    print(f"更新股票 {row['code']} 信息失败: {e}")
                    continue
            
            conn.commit()
            conn.close()
            print(f"股票信息更新完成，共 {len(stock_info)} 只股票")
            
        except Exception as e:
            print(f"更新股票信息失败: {e}")
    
    def collect_daily_data(self, stock_codes, start_date=None, end_date=None):
        """采集日线数据"""
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        conn = sqlite3.connect(self.db_path)
        
        for code in stock_codes:
            try:
                df = ak.stock_zh_a_hist(symbol=code, start_date=start_date, end_date=end_date)
                if df.empty:
                    continue
                
                # 重命名列
                df = df.rename(columns={
                    '日期': 'date', '开盘': 'open', '收盘': 'close',
                    '最高': 'high', '最低': 'low', '成交量': 'volume',
                    '成交额': 'amount', '振幅': 'amplitude', '涨跌幅': 'change_pct',
                    '涨跌额': 'change_amount', '换手率': 'turnover'
                })
                
                df['code'] = code
                df['date'] = pd.to_datetime(df['date']).dt.date
                
                # 插入数据库
                df.to_sql('daily_data', conn, if_exists='append', index=False, method='ignore')
                print(f"股票 {code} 日线数据采集完成")
                
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                print(f"采集股票 {code} 日线数据失败: {e}")
                continue
        
        conn.close()
    
    def collect_realtime_data(self, stock_codes):
        """采集实时数据"""
        try:
            # 获取实时行情
            realtime_data = ak.stock_zh_a_spot_em()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for _, row in realtime_data.iterrows():
                if row['代码'] in stock_codes:
                    cursor.execute('''
                        INSERT OR REPLACE INTO realtime_data 
                        (code, name, price, change_pct, change_amount, volume, amount,
                         high, low, open, pre_close, timestamp) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row['代码'], row['名称'], row['最新价'], row['涨跌幅'],
                        row['涨跌额'], row['成交量'], row['成交额'], row['最高'],
                        row['最低'], row['今开'], row['昨收'], datetime.now()
                    ))
            
            conn.commit()
            conn.close()
            print(f"实时数据更新完成，时间: {datetime.now()}")
            
        except Exception as e:
            print(f"采集实时数据失败: {e}")
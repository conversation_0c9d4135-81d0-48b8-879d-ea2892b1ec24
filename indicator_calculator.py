import pandas as pd
import sqlite3
import talib
import numpy as np
from datetime import datetime

class IndicatorCalculator:
    def __init__(self, db_path="stock_data.db"):
        self.db_path = db_path
    
    def calculate_indicators(self, code, days=100):
        """计算技术指标"""
        conn = sqlite3.connect(self.db_path)
        
        # 获取历史数据
        query = '''
            SELECT date, open, high, low, close, volume 
            FROM daily_data 
            WHERE code = ? 
            ORDER BY date DESC 
            LIMIT ?
        '''
        df = pd.read_sql_query(query, conn, params=(code, days))
        
        if len(df) < 20:
            conn.close()
            return
        
        df = df.sort_values('date')
        
        # 计算技术指标
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        # 移动平均线
        ma5 = talib.SMA(close, timeperiod=5)
        ma10 = talib.SMA(close, timeperiod=10)
        ma20 = talib.SMA(close, timeperiod=20)
        ma60 = talib.SMA(close, timeperiod=60)
        
        # RSI
        rsi = talib.RSI(close, timeperiod=14)
        
        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close)
        
        # 布林带
        boll_upper, boll_mid, boll_lower = talib.BBANDS(close)
        
        # 成交量均线
        volume_ma = talib.SMA(volume.astype(float), timeperiod=20)
        
        # 保存到数据库
        cursor = conn.cursor()
        for i, date in enumerate(df['date']):
            cursor.execute('''
                INSERT OR REPLACE INTO technical_indicators 
                (code, date, ma5, ma10, ma20, ma60, rsi, macd, macd_signal, 
                 macd_hist, boll_upper, boll_mid, boll_lower, volume_ma) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                code, date, 
                ma5[i] if not np.isnan(ma5[i]) else None,
                ma10[i] if not np.isnan(ma10[i]) else None,
                ma20[i] if not np.isnan(ma20[i]) else None,
                ma60[i] if not np.isnan(ma60[i]) else None,
                rsi[i] if not np.isnan(rsi[i]) else None,
                macd[i] if not np.isnan(macd[i]) else None,
                macd_signal[i] if not np.isnan(macd_signal[i]) else None,
                macd_hist[i] if not np.isnan(macd_hist[i]) else None,
                boll_upper[i] if not np.isnan(boll_upper[i]) else None,
                boll_mid[i] if not np.isnan(boll_mid[i]) else None,
                boll_lower[i] if not np.isnan(boll_lower[i]) else None,
                volume_ma[i] if not np.isnan(volume_ma[i]) else None
            ))
        
        conn.commit()
        conn.close()
        print(f"股票 {code} 技术指标计算完成")
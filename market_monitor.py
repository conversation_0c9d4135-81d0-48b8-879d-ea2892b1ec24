import schedule
import time
import threading
from database_setup import StockDatabase
from data_collector import DataCollector
from indicator_calculator import IndicatorCalculator
from signal_monitor import SignalMonitor

class MarketMonitor:
    def __init__(self):
        self.db = StockDatabase()
        self.collector = DataCollector()
        self.calculator = IndicatorCalculator()
        self.monitor = SignalMonitor()
        self.stock_codes = []
        
    def initialize(self):
        """初始化监控系统"""
        print("初始化市场监控系统...")
        
        # 更新股票信息
        self.collector.update_stock_info()
        
        # 获取股票代码（可以筛选特定股票）
        self.stock_codes = self.collector.get_all_stock_codes()[:100]  # 限制前100只
        
        # 采集历史数据
        print("采集历史数据...")
        self.collector.collect_daily_data(self.stock_codes)
        
        # 计算技术指标
        print("计算技术指标...")
        for code in self.stock_codes:
            self.calculator.calculate_indicators(code)
            time.sleep(0.1)
        
        print("初始化完成")
    
    def update_realtime_data(self):
        """更新实时数据"""
        print("更新实时数据...")
        self.collector.collect_realtime_data(self.stock_codes)
    
    def scan_signals(self):
        """扫描交易信号"""
        print("扫描交易信号...")
        
        # 扫描金叉信号
        golden_signals = self.monitor.scan_golden_cross_signals()
        if not golden_signals.empty:
            print(f"发现 {len(golden_signals)} 个金叉信号")
            print(golden_signals[['code', 'name', 'price']])
        
        # 扫描放量突破信号
        volume_signals = self.monitor.scan_volume_breakout()
        if not volume_signals.empty:
            print(f"发现 {len(volume_signals)} 个放量突破信号")
            print(volume_signals[['code', 'name', 'close', 'change_pct']])
    
    def daily_update(self):
        """每日数据更新"""
        print("执行每日数据更新...")
        self.collector.collect_daily_data(self.stock_codes)
        
        for code in self.stock_codes:
            self.calculator.calculate_indicators(code)
            time.sleep(0.1)
    
    def start_monitoring(self):
        """启动监控"""
        # 调度任务
        schedule.every().day.at("09:00").do(self.daily_update)
        schedule.every(5).minutes.do(self.update_realtime_data)
        schedule.every(10).minutes.do(self.scan_signals)
        
        print("市场监控系统启动...")
        print("实时数据更新: 每5分钟")
        print("信号扫描: 每10分钟")
        print("日线数据更新: 每日09:00")
        
        while True:
            schedule.run_pending()
            time.sleep(60)

def main():
    monitor = MarketMonitor()
    
    # 初始化（首次运行）
    monitor.initialize()
    
    # 启动监控
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
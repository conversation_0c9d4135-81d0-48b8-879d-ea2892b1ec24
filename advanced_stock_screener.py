import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class AdvancedStockScreener:
    def __init__(self, max_workers=5):
        self.stock_list = []
        self.max_workers = max_workers
        self.lock = threading.Lock()
        self.progress_count = 0
        
    def get_all_stocks(self):
        """获取全市场股票列表"""
        try:
            stock_info = ak.stock_info_a_code_name()
            # 过滤掉ST、*ST股票
            stock_info = stock_info[~stock_info['name'].str.contains('ST|退', na=False)]
            self.stock_list = stock_info[['code', 'name']].to_dict('records')
            print(f"获取到 {len(self.stock_list)} 只股票（已过滤ST股票）")
            return self.stock_list
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def get_stock_data(self, code, days=60):
        """获取单只股票数据"""
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            df = ak.stock_zh_a_hist(symbol=code, start_date=start_date, end_date=end_date)
            if df.empty:
                return None
                
            df = df.rename(columns={
                '日期': 'date', '开盘': 'open', '收盘': 'close',
                '最高': 'high', '最低': 'low', '成交量': 'volume',
                '成交额': 'amount', '涨跌幅': 'change_pct', '换手率': 'turnover'
            })
            
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            return df
            
        except Exception as e:
            return None
    
    def calculate_technical_indicators(self, df):
        """计算所有技术指标"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # MACD
        exp1 = close.ewm(span=12).mean()
        exp2 = close.ewm(span=26).mean()
        macd = exp1 - exp2
        macd_signal = macd.ewm(span=9).mean()
        macd_hist = macd - macd_signal
        
        # 布林带
        sma20 = close.rolling(window=20).mean()
        std20 = close.rolling(window=20).std()
        boll_upper = sma20 + (std20 * 2)
        boll_lower = sma20 - (std20 * 2)
        
        # 移动平均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()
        
        # 成交量指标
        volume_ma20 = volume.rolling(window=20).mean()
        
        # RSI
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return {
            'macd': macd, 'macd_signal': macd_signal, 'macd_hist': macd_hist,
            'boll_upper': boll_upper, 'boll_mid': sma20, 'boll_lower': boll_lower,
            'ma5': ma5, 'ma10': ma10, 'ma20': ma20,
            'volume_ma20': volume_ma20, 'rsi': rsi
        }
    
    def check_macd_boll_volume_strategy(self, df, indicators):
        """检查MACDBollVolumeStrategy条件"""
        if len(df) < 30:
            return False, {}
        
        # 检查最近20天是否有MACD金叉
        has_golden_cross = False
        golden_cross_date = None
        
        for i in range(max(0, len(df)-20), len(df)):
            if i >= 1:
                if (indicators['macd'].iloc[i] > indicators['macd_signal'].iloc[i] and 
                    indicators['macd_signal'].iloc[i-1] >= indicators['macd'].iloc[i-1] and
                    indicators['macd'].iloc[i] < 0):
                    has_golden_cross = True
                    golden_cross_date = df['date'].iloc[i]
                    break
        
        # 检查金叉后是否有大阳线+高成交量
        has_big_yang_volume = False
        big_yang_date = None
        
        if has_golden_cross:
            for i in range(max(0, len(df)-15), len(df)):
                if i < len(df):
                    row = df.iloc[i]
                    change_ratio = (row['close'] - row['open']) / row['open']
                    volume_ratio = row['volume'] / indicators['volume_ma20'].iloc[i] if not pd.isna(indicators['volume_ma20'].iloc[i]) else 0
                    
                    if change_ratio >= 0.03 and volume_ratio >= 2.0:
                        has_big_yang_volume = True
                        big_yang_date = row['date']
                        break
        
        # 当前状态检查
        current_price = df['close'].iloc[-1]
        current_macd = indicators['macd'].iloc[-1]
        current_macd_signal = indicators['macd_signal'].iloc[-1]
        current_boll_mid = indicators['boll_mid'].iloc[-1]
        current_boll_upper = indicators['boll_upper'].iloc[-1]
        
        # MACD向上趋势
        macd_uptrend = (len(indicators['macd']) >= 3 and 
                       indicators['macd'].iloc[-1] > indicators['macd'].iloc[-2] > indicators['macd'].iloc[-3])
        
        # 价格在布林带中轨与上轨之间
        price_in_range = current_boll_mid <= current_price <= current_boll_upper
        
        # MACD在信号线之上
        macd_above_signal = current_macd > current_macd_signal
        
        # 综合条件
        conditions = {
            'has_golden_cross': has_golden_cross,
            'has_big_yang_volume': has_big_yang_volume,
            'macd_uptrend': macd_uptrend,
            'price_in_range': price_in_range,
            'macd_above_signal': macd_above_signal,
            'golden_cross_date': golden_cross_date,
            'big_yang_date': big_yang_date
        }
        
        # 策略条件
        strategy_match = (has_golden_cross and has_big_yang_volume and 
                         macd_uptrend and price_in_range and macd_above_signal)
        
        return strategy_match, conditions
    
    def screen_single_stock(self, stock_info):
        """筛选单只股票"""
        code = stock_info['code']
        name = stock_info['name']
        
        try:
            # 获取股票数据
            df = self.get_stock_data(code)
            if df is None or len(df) < 30:
                return None
            
            # 计算技术指标
            indicators = self.calculate_technical_indicators(df)
            
            # 检查策略条件
            is_match, conditions = self.check_macd_boll_volume_strategy(df, indicators)
            
            if is_match:
                # 计算额外指标
                volume_ratio = df['volume'].iloc[-1] / indicators['volume_ma20'].iloc[-1] if not pd.isna(indicators['volume_ma20'].iloc[-1]) else 0
                
                return {
                    'code': code,
                    'name': name,
                    'price': df['close'].iloc[-1],
                    'change_pct': df['change_pct'].iloc[-1],
                    'volume_ratio': volume_ratio,
                    'turnover': df['turnover'].iloc[-1] if 'turnover' in df.columns else 0,
                    'macd': indicators['macd'].iloc[-1],
                    'rsi': indicators['rsi'].iloc[-1] if not pd.isna(indicators['rsi'].iloc[-1]) else 0,
                    'ma5': indicators['ma5'].iloc[-1],
                    'ma20': indicators['ma20'].iloc[-1],
                    'conditions': conditions,
                    'score': self.calculate_stock_score(df, indicators)
                }
            
        except Exception as e:
            pass
        
        return None
    
    def calculate_stock_score(self, df, indicators):
        """计算股票评分"""
        score = 0
        
        # 技术指标评分
        if indicators['ma5'].iloc[-1] > indicators['ma20'].iloc[-1]:
            score += 1
        if 30 < indicators['rsi'].iloc[-1] < 70:
            score += 1
        if indicators['macd'].iloc[-1] > 0:
            score += 1
        if df['change_pct'].iloc[-1] > 0:
            score += 1
        
        # 成交量评分
        volume_ratio = df['volume'].iloc[-1] / indicators['volume_ma20'].iloc[-1]
        if volume_ratio > 1.5:
            score += 1
        
        return score
    
    def run_screening_parallel(self, max_stocks=None):
        """并行运行选股"""
        print("开始全市场选股...")
        
        stocks = self.get_all_stocks()
        if not stocks:
            return []
        
        if max_stocks:
            stocks = stocks[:max_stocks]
            print(f"限制分析前 {max_stocks} 只股票")
        
        selected_stocks = []
        self.progress_count = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_stock = {executor.submit(self.screen_single_stock, stock): stock 
                              for stock in stocks}
            
            # 处理完成的任务
            for future in as_completed(future_to_stock):
                stock = future_to_stock[future]
                
                with self.lock:
                    self.progress_count += 1
                    if self.progress_count % 50 == 0:
                        print(f"已分析 {self.progress_count}/{len(stocks)} 只股票")
                
                try:
                    result = future.result()
                    if result:
                        selected_stocks.append(result)
                        print(f"✓ 发现符合条件的股票: {result['code']} {result['name']} - "
                              f"价格: {result['price']:.2f}, 评分: {result['score']}")
                except Exception as e:
                    pass
                
                # 控制请求频率
                time.sleep(0.05)
        
        # 按评分排序
        selected_stocks.sort(key=lambda x: x['score'], reverse=True)
        return selected_stocks
    
    def save_results(self, results, filename=None):
        """保存选股结果"""
        if not results:
            print("没有找到符合条件的股票")
            return
        
        if filename is None:
            filename = f"macd_boll_volume_stocks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 保存CSV
        df = pd.DataFrame(results)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        # 保存详细JSON
        json_filename = filename.replace('.csv', '_detail.json')
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"选股结果已保存到: {filename}")
        print(f"详细信息已保存到: {json_filename}")
        
        # 显示结果摘要
        self.print_summary(results)
    
    def print_summary(self, results):
        """打印结果摘要"""
        if not results:
            return
        
        df = pd.DataFrame(results)
        
        print(f"\n=== MACDBollVolumeStrategy选股结果摘要 ===")
        print(f"符合条件的股票数量: {len(results)}")
        print(f"平均涨跌幅: {df['change_pct'].mean():.2f}%")
        print(f"平均成交量倍数: {df['volume_ratio'].mean():.2f}")
        print(f"平均评分: {df['score'].mean():.2f}")
        
        print(f"\n=== 前20只高评分股票 ===")
        for i, stock in enumerate(results[:20]):
            print(f"{i+1:2d}. {stock['code']} {stock['name']:8s} - "
                  f"价格: {stock['price']:6.2f}, 涨跌幅: {stock['change_pct']:5.2f}%, "
                  f"评分: {stock['score']}, 成交量倍数: {stock['volume_ratio']:.2f}")

def main():
    screener = AdvancedStockScreener(max_workers=3)
    
    print("=== MACDBollVolumeStrategy全市场选股系统 ===")
    
    # 选择运行模式
    mode = input("选择运行模式 (1-测试200只股票, 2-全市场扫描): ")
    
    if mode == "1":
        results = screener.run_screening_parallel(max_stocks=200)
    else:
        results = screener.run_screening_parallel()
    
    # 保存结果
    screener.save_results(results)
    
    print("\n选股完成！")

if __name__ == "__main__":
    main()
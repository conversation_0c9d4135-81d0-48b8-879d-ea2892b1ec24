import backtrader as bt

class PortfolioManager(bt.Strategy):
    """投资组合管理器"""
    params = (
        ('rebalance_freq', 20),  # 重平衡频率
        ('max_positions', 5),    # 最大持仓数量
        ('position_size', 0.2),  # 每个位置的资金比例
    )
    
    def __init__(self):
        self.rebalance_counter = 0
        self.stock_scores = {}
        
    def calculate_stock_scores(self):
        """计算股票评分（可以基于多个指标）"""
        scores = {}
        for data in self.datas:
            # 示例：基于RSI和MA的综合评分
            rsi = bt.indicators.RSI(data.close, period=14)
            ma = bt.indicators.SimpleMovingAverage(data.close, period=20)
            
            # 简单评分逻辑
            score = 0
            if data.close[0] > ma[0]:
                score += 1
            if 30 < rsi[0] < 70:
                score += 1
                
            scores[data] = score
        return scores
    
    def rebalance_portfolio(self):
        """重平衡投资组合"""
        scores = self.calculate_stock_scores()
        # 选择评分最高的股票
        sorted_stocks = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 卖出不在前N名的股票
        for data in self.datas:
            if self.getposition(data).size > 0:
                if data not in [stock[0] for stock in sorted_stocks[:self.params.max_positions]]:
                    self.close(data=data)
        
        # 买入前N名股票
        target_value = self.broker.getvalue() * self.params.position_size
        for stock, score in sorted_stocks[:self.params.max_positions]:
            if self.getposition(stock).size == 0:
                size = int(target_value / stock.close[0] / 100) * 100
                if size > 0:
                    self.buy(data=stock, size=size)
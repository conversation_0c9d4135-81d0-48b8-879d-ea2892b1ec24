import sqlite3
import pandas as pd
from datetime import datetime
import time

class SignalMonitor:
    def __init__(self, db_path="stock_data.db"):
        self.db_path = db_path
    
    def scan_golden_cross_signals(self):
        """扫描金叉信号"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT t1.code, t1.date, t1.ma5, t1.ma20, t1.macd, t1.macd_signal,
                   r.price, r.name
            FROM technical_indicators t1
            JOIN technical_indicators t2 ON t1.code = t2.code 
                AND date(t1.date) = date(t2.date, '+1 day')
            JOIN realtime_data r ON t1.code = r.code
            WHERE t1.ma5 > t1.ma20 
                AND t2.ma5 <= t2.ma20
                AND t1.macd > t1.macd_signal
                AND t2.macd <= t2.macd_signal
                AND date(t1.date) = date('now')
        '''
        
        signals = pd.read_sql_query(query, conn)
        
        for _, signal in signals.iterrows():
            self.save_signal(
                signal['code'],
                'GOLDEN_CROSS',
                3,  # 强度
                signal['price'],
                f"MA5金叉MA20 + MACD金叉，股票: {signal['name']}"
            )
        
        conn.close()
        return signals
    
    def scan_volume_breakout(self):
        """扫描放量突破信号"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT d.code, d.date, d.volume, d.close, d.change_pct,
                   t.volume_ma, t.boll_upper, r.name
            FROM daily_data d
            JOIN technical_indicators t ON d.code = t.code AND d.date = t.date
            JOIN realtime_data r ON d.code = r.code
            WHERE d.volume > t.volume_ma * 2
                AND d.close > t.boll_upper
                AND d.change_pct > 3
                AND date(d.date) = date('now')
        '''
        
        signals = pd.read_sql_query(query, conn)
        
        for _, signal in signals.iterrows():
            self.save_signal(
                signal['code'],
                'VOLUME_BREAKOUT',
                4,  # 强度
                signal['close'],
                f"放量突破布林上轨，涨幅: {signal['change_pct']:.2f}%，股票: {signal['name']}"
            )
        
        conn.close()
        return signals
    
    def save_signal(self, code, signal_type, strength, price, description):
        """保存信号到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO signals (code, signal_type, signal_strength, price, description)
            VALUES (?, ?, ?, ?, ?)
        ''', (code, signal_type, strength, price, description))
        
        conn.commit()
        conn.close()
    
    def get_recent_signals(self, hours=24):
        """获取最近的信号"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT * FROM signals 
            WHERE created_at > datetime('now', '-{} hours')
            ORDER BY created_at DESC
        '''.format(hours)
        
        signals = pd.read_sql_query(query, conn)
        conn.close()
        
        return signals
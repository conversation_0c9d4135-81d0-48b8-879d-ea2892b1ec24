class StrategyFactory:
    """策略工厂"""
    
    @staticmethod
    def create_strategy(strategy_name, **params):
        strategies = {
            'ma20': MA20Strategy,
            'macd': MACDStrategy,
            'rsi': RSIStrategy,
            'portfolio': PortfolioManager,
            'macd_boll_volume': MACDBollVolumeStrategy,
        }
        
        if strategy_name not in strategies:
            raise ValueError(f"未知策略: {strategy_name}")
        
        return strategies[strategy_name], params
    
    @staticmethod
    def get_available_strategies():
        return ['ma20', 'macd', 'rsi', 'portfolio', 'macd_boll_volume']

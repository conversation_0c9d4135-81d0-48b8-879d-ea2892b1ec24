import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

class StockScreener:
    def __init__(self):
        self.stock_list = []
        self.sector_stocks = {}
        
    def get_all_stocks(self):
        """获取全市场股票列表"""
        try:
            # 获取A股股票列表
            stock_info = ak.stock_info_a_code_name()
            self.stock_list = stock_info[['code', 'name']].to_dict('records')
            print(f"获取到 {len(self.stock_list)} 只股票")
            return self.stock_list
        except Exception as e:
            print(f"获取股票列表失败: {e}")
            return []
    
    def get_sector_stocks(self, sector_name):
        """获取指定板块的成分股"""
        print(f"正在获取板块 '{sector_name}' 的成分股...")
        
        try:
            # 方法1：尝试东方财富概念板块
            try:
                print(f"尝试东方财富概念板块查询: {sector_name}")
                concept_stocks = ak.stock_board_concept_cons_em(symbol=sector_name)
                if not concept_stocks.empty:
                    print(f"✓ 东方财富概念板块查询成功，找到 {len(concept_stocks)} 只股票")
                    stocks = concept_stocks[['代码', '名称']].rename(columns={'代码': 'code', '名称': 'name'})
                    return stocks.to_dict('records')
            except Exception as e1:
                print(f"✗ 东方财富概念板块查询失败: {str(e1)[:100]}")
            
            # 方法2：尝试东方财富行业板块映射
            industry_mapping = {
                '新能源': '新能源', '锂电池': '锂电池', '光伏': '光伏概念',
                '风电': '风电', '储能': '储能', '氢能源': '氢能源',
                '人工智能': '人工智能', '芯片': '芯片概念', '半导体': '半导体及元件',
                '5G': '5G概念', '物联网': '物联网', '云计算': '云计算',
                '新能源汽车': '新能源汽车', '智能驾驶': '无人驾驶', '充电桩': '充电桩',
                '医药': '医药制造', '创新药': '创新药', '医疗器械': '医疗器械',
                '银行': '银行', '房地产': '房地产开发', '白酒': '白酒',
                '汽车': '汽车整车', '钢铁': '钢铁', '煤炭': '煤炭开采',
                '有色': '有色金属', '电力': '电力', '石油': '石油开采',
                '化工': '化工', '军工': '国防军工', '计算机': '计算机应用',
                '通信': '通信设备', '电子': '电子制造', '机械': '机械设备'
            }
            
            # 尝试映射后的名称
            mapped_name = industry_mapping.get(sector_name, sector_name)
            if mapped_name != sector_name:
                try:
                    print(f"尝试映射名称 '{mapped_name}' 的概念板块查询")
                    concept_stocks = ak.stock_board_concept_cons_em(symbol=mapped_name)
                    if not concept_stocks.empty:
                        print(f"✓ 映射名称概念板块查询成功，找到 {len(concept_stocks)} 只股票")
                        stocks = concept_stocks[['代码', '名称']].rename(columns={'代码': 'code', '名称': 'name'})
                        return stocks.to_dict('records')
                except Exception as e2:
                    print(f"✗ 映射名称概念板块查询失败: {str(e2)[:100]}")
            
            # 方法3：尝试东方财富行业板块
            industry_mapping_2 = {
                '银行': '银行', '房地产': '房地产开发', '医药': '医药生物',
                '白酒': '白酒', '汽车': '汽车整车', '钢铁': '钢铁',
                '煤炭': '煤炭开采', '有色': '有色金属', '电力': '电力',
                '石油': '石油开采', '化工': '化工', '军工': '国防军工',
                '半导体': '半导体', '计算机': '计算机应用', '通信': '通信设备',
                '电子': '电子制造', '机械': '机械设备', '建筑': '建筑装饰',
                '交通': '交通运输', '食品': '食品饮料', '纺织': '纺织服装',
                '家电': '家用电器', '传媒': '传媒', '环保': '环保',
                '农业': '农林牧渔', '保险': '保险', '证券': '证券'
            }
            
            if sector_name in industry_mapping_2:
                try:
                    print(f"尝试东方财富行业板块查询: {industry_mapping_2[sector_name]}")
                    industry_stocks = ak.stock_board_industry_cons_em(symbol=industry_mapping_2[sector_name])
                    if not industry_stocks.empty:
                        print(f"✓ 东方财富行业板块查询成功，找到 {len(industry_stocks)} 只股票")
                        stocks = industry_stocks[['代码', '名称']].rename(columns={'代码': 'code', '名称': 'name'})
                        return stocks.to_dict('records')
                except Exception as e3:
                    print(f"✗ 东方财富行业板块查询失败: {str(e3)[:100]}")
            
            # 方法4：尝试模糊匹配概念板块
            try:
                print("尝试获取所有概念板块进行模糊匹配...")
                all_concepts = ak.stock_board_concept_name_em()
                matching_concepts = all_concepts[all_concepts['板块名称'].str.contains(sector_name, na=False)]
                
                if not matching_concepts.empty:
                    best_match = matching_concepts.iloc[0]['板块名称']
                    print(f"找到匹配的概念板块: {best_match}")
                    
                    concept_stocks = ak.stock_board_concept_cons_em(symbol=best_match)
                    if not concept_stocks.empty:
                        print(f"✓ 模糊匹配概念板块查询成功，找到 {len(concept_stocks)} 只股票")
                        stocks = concept_stocks[['代码', '名称']].rename(columns={'代码': 'code', '名称': 'name'})
                        return stocks.to_dict('records')
            except Exception as e4:
                print(f"✗ 模糊匹配查询失败: {str(e4)[:100]}")
            
            # 方法5：使用离线板块数据作为最后备选
            print(f"所有在线方法都失败，使用离线板块数据: {sector_name}")
            offline_stocks = self.get_sector_stocks_offline(sector_name)
            if offline_stocks:
                print(f"✓ 离线数据查询成功，找到 {len(offline_stocks)} 只股票")
                return offline_stocks
            
            print(f"✗ 所有方法都失败，未找到板块 '{sector_name}' 的成分股")
            return []
            
        except Exception as e:
            print(f"获取板块 '{sector_name}' 成分股时发生错误: {e}")
            return []
    
    def get_available_sectors(self):
        """获取可用的板块列表"""
        print("正在获取板块列表...")
        
        try:
            # 方法1：尝试东方财富接口
            print("尝试获取东方财富概念板块...")
            concept_boards = ak.stock_board_concept_name_em()
            concept_list = concept_boards['板块名称'].tolist()[:30]
            
            print("尝试获取东方财富行业板块...")
            industry_boards = ak.stock_board_industry_name_em()
            industry_list = industry_boards['板块名称'].tolist()[:30]
            
            print("\n=== 东方财富概念板块 ===")
            for i, name in enumerate(concept_list, 1):
                print(f"{i:2d}. {name}")
            
            print("\n=== 东方财富行业板块 ===")
            for i, name in enumerate(industry_list, 1):
                print(f"{i:2d}. {name}")
            
            return concept_list + industry_list
            
        except Exception as e:
            print(f"东方财富获取板块失败: {e}")
            
            try:
                # 方法2：尝试同花顺接口
                print("尝试获取同花顺概念板块...")
                ths_concepts = ak.stock_board_concept_name_ths()
                print(f"同花顺数据列名: {ths_concepts.columns.tolist()}")
                
                # 尝试不同的列名
                possible_columns = ['概念名称', 'name', '名称', 'concept_name', 'board_name']
                ths_concept_list = []
                
                for col in possible_columns:
                    if col in ths_concepts.columns:
                        ths_concept_list = ths_concepts[col].tolist()[:30]
                        print(f"使用列名: {col}")
                        break
                
                if ths_concept_list:
                    print("\n=== 同花顺概念板块 ===")
                    for i, name in enumerate(ths_concept_list, 1):
                        print(f"{i:2d}. {name}")
                    
                    return ths_concept_list
                else:
                    print("未找到合适的列名")
                    
            except Exception as e2:
                print(f"同花顺获取板块失败: {e2}")
                
        print("使用备用板块列表...")
        
        # 方法3：备用板块列表
        concept_sectors = [
            "新能源", "锂电池", "光伏", "风电", "储能", "氢能源",
            "人工智能", "芯片", "半导体", "5G", "物联网", "云计算", 
            "新能源汽车", "智能驾驶", "充电桩", "动力电池",
            "医美", "CRO", "创新药", "医疗器械", "疫苗",
            "数字货币", "区块链", "元宇宙", "虚拟现实"
        ]
        
        industry_sectors = [
            "银行", "房地产", "医药", "白酒", "汽车", "钢铁",
            "煤炭", "有色", "电力", "石油", "化工", "军工",
            "半导体", "计算机", "通信", "电子", "机械", "建筑",
            "交通", "食品", "纺织", "家电", "传媒", "环保", "农业"
        ]
        
        print("\n=== 备用概念板块 ===")
        for i, name in enumerate(concept_sectors, 1):
            print(f"{i:2d}. {name}")
        
        print("\n=== 备用行业板块 ===")
        for i, name in enumerate(industry_sectors, 1):
            print(f"{i:2d}. {name}")
        
        print("\n=== 推荐板块组合 ===")
        print("新能源: 新能源,锂电池,光伏")
        print("科技: 人工智能,芯片,半导体")
        print("汽车: 新能源汽车,智能驾驶,充电桩")
        print("医药: 医药,创新药,CRO")
        print("金融: 银行,保险,证券")
        print("消费: 白酒,食品,家电")
        
        return concept_sectors + industry_sectors
    
    def run_sector_screening(self, sector_names):
        """运行板块选股"""
        print(f"开始板块选股: {', '.join(sector_names)}")
        
        all_sector_stocks = []
        
        # 获取所有板块的成分股
        for sector_name in sector_names:
            print(f"\n获取板块 '{sector_name}' 的成分股...")
            sector_stocks = self.get_sector_stocks(sector_name)
            
            if sector_stocks:
                print(f"板块 '{sector_name}' 包含 {len(sector_stocks)} 只股票")
                # 为每只股票添加板块信息
                for stock in sector_stocks:
                    stock['sector'] = sector_name
                all_sector_stocks.extend(sector_stocks)
            else:
                print(f"板块 '{sector_name}' 未找到成分股")
        
        if not all_sector_stocks:
            print("未获取到任何板块成分股")
            return []
        
        # 去重（同一只股票可能属于多个板块）
        unique_stocks = {}
        for stock in all_sector_stocks:
            code = stock['code']
            if code not in unique_stocks:
                unique_stocks[code] = stock
            else:
                # 合并板块信息
                existing_sector = unique_stocks[code].get('sector', '')
                new_sector = stock['sector']
                if new_sector not in existing_sector:
                    unique_stocks[code]['sector'] = f"{existing_sector}, {new_sector}"
        
        stocks_to_analyze = list(unique_stocks.values())
        print(f"\n去重后共 {len(stocks_to_analyze)} 只股票待分析")
        
        selected_stocks = []
        
        for i, stock in enumerate(stocks_to_analyze):
            print(f"分析进度: {i+1}/{len(stocks_to_analyze)} - {stock['code']} {stock['name']} ({stock['sector']})")
            
            result = self.screen_stock(stock)
            if result:
                result['sector'] = stock['sector']  # 添加板块信息
                selected_stocks.append(result)
                print(f"✓ 发现符合条件的股票: {result['code']} {result['name']} - 价格: {result['price']:.2f}")
            
            # 避免请求过快
            time.sleep(0.1)
            
            # 每分析50只股票显示一次进度
            if (i + 1) % 50 == 0:
                print(f"已分析 {i+1} 只股票，发现 {len(selected_stocks)} 只符合条件")
        
        return selected_stocks
    
    def get_stock_data(self, code, days=60):
        """获取单只股票数据"""
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            df = ak.stock_zh_a_hist(symbol=code, start_date=start_date, end_date=end_date)
            if df.empty:
                return None
                
            df = df.rename(columns={
                '日期': 'date', '开盘': 'open', '收盘': 'close',
                '最高': 'high', '最低': 'low', '成交量': 'volume',
                '成交额': 'amount', '涨跌幅': 'change_pct', '换手率': 'turnover'
            })
            
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            return df
            
        except Exception as e:
            print(f"获取股票 {code} 数据失败: {e}")
            return None
    
    def calculate_macd(self, close_prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        exp1 = close_prices.ewm(span=fast).mean()
        exp2 = close_prices.ewm(span=slow).mean()
        macd = exp1 - exp2
        macd_signal = macd.ewm(span=signal).mean()
        macd_hist = macd - macd_signal
        return macd, macd_signal, macd_hist
    
    def calculate_bollinger_bands(self, close_prices, period=20, std_dev=2):
        """计算布林带"""
        sma = close_prices.rolling(window=period).mean()
        std = close_prices.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def is_macd_golden_cross(self, macd, macd_signal):
        """检测MACD金叉"""
        if len(macd) < 2:
            return False
        return (macd.iloc[-1] > macd_signal.iloc[-1] and 
                macd.iloc[-2] <= macd_signal.iloc[-2] and
                macd.iloc[-1] < 0)  # 低位金叉
    
    def is_big_yang_candle(self, df, threshold=0.03):
        """检测大阳线"""
        if df.empty:
            return False
        latest = df.iloc[-1]
        change_ratio = (latest['close'] - latest['open']) / latest['open']
        return change_ratio >= threshold
    
    def is_high_volume(self, df, ratio=2.0, period=20):
        """检测巨量成交"""
        if len(df) < period:
            return False
        volume_ma = df['volume'].rolling(window=period).mean()
        return df['volume'].iloc[-1] >= volume_ma.iloc[-1] * ratio
    
    def is_price_between_mid_upper(self, close_price, boll_mid, boll_upper):
        """检测股价是否在布林带中轨与上轨之间"""
        return boll_mid <= close_price <= boll_upper
    
    def check_macd_uptrend(self, macd):
        """检查MACD是否保持向上趋势"""
        if len(macd) < 3:
            return False
        return macd.iloc[-1] > macd.iloc[-2] > macd.iloc[-3]
    
    def is_price_near_upper_band(self, df, boll_upper, boll_mid, lookback_days=7, threshold=0.1):
        """检测前N天内是否有股价接近上轨"""
        if len(df) < lookback_days:
            return False
        
        for i in range(max(0, len(df)-lookback_days), len(df)-1):  # 排除当天
            price = df['close'].iloc[i]
            upper = boll_upper.iloc[i]
            mid = boll_mid.iloc[i]
            band_width = upper - mid
            
            # 检查是否接近上轨（在上轨的10%范围内）
            distance_to_upper = abs(price - upper)
            if distance_to_upper <= band_width * threshold:
                return True
        return False
    
    def is_price_near_mid_with_low_volume(self, df, boll_mid, boll_upper, volume_threshold=0.8):
        """检测当前股价是否缩量下降至接近中轨"""
        if len(df) < 5:
            return False
        
        current_price = df['close'].iloc[-1]
        current_mid = boll_mid.iloc[-1]
        current_upper = boll_upper.iloc[-1]
        band_width = current_upper - current_mid
        
        # 检查是否接近中轨（在中轨的20%范围内）
        distance_to_mid = abs(current_price - current_mid)
        is_near_mid = distance_to_mid <= band_width * 0.2
        
        # 检查是否缩量（当前成交量小于前5天平均成交量的80%）
        current_volume = df['volume'].iloc[-1]
        avg_volume_5d = df['volume'].iloc[-5:].mean()
        is_low_volume = current_volume <= avg_volume_5d * volume_threshold
        
        # 检查是否下降趋势（当前价格低于前3天最高价）
        recent_high = df['close'].iloc[-3:].max()
        is_declining = current_price < recent_high
        
        return is_near_mid and is_low_volume and is_declining

    def screen_stock(self, stock_info):
        """对单只股票进行筛选"""
        code = stock_info['code']
        name = stock_info['name']
        
        # 获取股票数据
        df = self.get_stock_data(code)
        if df is None or len(df) < 30:
            return None
        
        try:
            # 计算技术指标
            macd, macd_signal, macd_hist = self.calculate_macd(df['close'])
            boll_upper, boll_mid, boll_lower = self.calculate_bollinger_bands(df['close'])
            
            # 检查最近20天是否有MACD金叉
            has_golden_cross = False
            golden_cross_date = None
            for i in range(max(0, len(df)-20), len(df)):
                if i >= 1:
                    if (macd.iloc[i] > macd_signal.iloc[i] and 
                        macd.iloc[i-1] <= macd_signal.iloc[i-1] and
                        macd.iloc[i] < 0):
                        has_golden_cross = True
                        golden_cross_date = df['date'].iloc[i]
                        break
            
            # 检查金叉后是否有大阳线+高成交量
            has_big_yang_volume = False
            big_yang_date = None
            if has_golden_cross:
                for i in range(max(0, len(df)-15), len(df)):
                    if i < len(df):
                        row = df.iloc[i]
                        change_ratio = (row['close'] - row['open']) / row['open']
                        volume_ma = df['volume'].iloc[max(0, i-19):i+1].mean()
                        
                        if (change_ratio >= 0.03 and 
                            row['volume'] >= volume_ma * 2.0):
                            has_big_yang_volume = True
                            big_yang_date = row['date']
                            break
            
            # 当前状态检查
            current_price = df['close'].iloc[-1]
            current_macd = macd.iloc[-1]
            current_macd_signal = macd_signal.iloc[-1]
            current_boll_mid = boll_mid.iloc[-1]
            current_boll_upper = boll_upper.iloc[-1]
            
            # 新增条件：前7天内有股价位于上轨附近，现在股价缩量下降至接近中轨
            was_near_upper = self.is_price_near_upper_band(df, boll_upper, boll_mid, lookback_days=7)
            now_near_mid_low_volume = self.is_price_near_mid_with_low_volume(df, boll_mid, boll_upper)
            
            # 综合条件判断
            conditions = {
                'has_golden_cross': has_golden_cross,
                'has_big_yang_volume': has_big_yang_volume,
                'macd_uptrend': self.check_macd_uptrend(macd),
                'price_in_range': self.is_price_between_mid_upper(current_price, current_boll_mid, current_boll_upper),
                'macd_above_signal': current_macd > current_macd_signal,
                'was_near_upper': was_near_upper,  # 新增条件
                'now_near_mid_low_volume': now_near_mid_low_volume,  # 新增条件
                'golden_cross_date': golden_cross_date,
                'big_yang_date': big_yang_date
            }
            
            # 满足条件的股票（包含新增条件）
            if (conditions['has_golden_cross'] and 
                conditions['has_big_yang_volume'] and
                conditions['macd_uptrend'] and
                conditions['price_in_range'] and
                conditions['macd_above_signal'] and
                conditions['was_near_upper'] and  # 新增条件
                conditions['now_near_mid_low_volume']):  # 新增条件
                
                return {
                    'code': code,
                    'name': name,
                    'price': current_price,
                    'change_pct': df['change_pct'].iloc[-1],
                    'volume_ratio': df['volume'].iloc[-1] / df['volume'].rolling(20).mean().iloc[-1],
                    'macd': current_macd,
                    'turnover': df['turnover'].iloc[-1] if 'turnover' in df.columns else 0,
                    'conditions': conditions,
                    'score': self.calculate_stock_score(df, macd, boll_mid, boll_upper)
                }
                
        except Exception as e:
            print(f"分析股票 {code} 时出错: {e}")
            
        return None
    
    def run_screening(self, max_stocks=None):
        """运行全市场选股"""
        print("开始全市场选股...")
        
        # 获取股票列表
        stocks = self.get_all_stocks()
        if not stocks:
            return []
        
        if max_stocks:
            stocks = stocks[:max_stocks]
            print(f"限制分析前 {max_stocks} 只股票")
        
        selected_stocks = []
        
        for i, stock in enumerate(stocks):
            print(f"分析进度: {i+1}/{len(stocks)} - {stock['code']} {stock['name']}")
            
            result = self.screen_stock(stock)
            if result:
                selected_stocks.append(result)
                print(f"✓ 发现符合条件的股票: {result['code']} {result['name']} - 价格: {result['price']:.2f}")
            
            # 避免请求过快
            time.sleep(0.1)
            
            # 每分析100只股票显示一次进度
            if (i + 1) % 100 == 0:
                print(f"已分析 {i+1} 只股票，发现 {len(selected_stocks)} 只符合条件")
        
        return selected_stocks
    
    def save_results(self, results, filename=None):
        """保存选股结果"""
        if not results:
            print("没有找到符合条件的股票")
            return
        
        df = pd.DataFrame(results)
        
        if filename is None:
            filename = f"selected_stocks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"选股结果已保存到: {filename}")
        
        # 显示结果摘要
        print(f"\n=== 选股结果摘要 ===")
        print(f"符合条件的股票数量: {len(results)}")
        print(f"平均涨跌幅: {df['change_pct'].mean():.2f}%")
        print(f"平均成交量倍数: {df['volume_ratio'].mean():.2f}")
        
        # 按板块分组显示
        if 'sector' in df.columns:
            print(f"\n=== 按板块分布 ===")
            sector_counts = df['sector'].value_counts()
            for sector, count in sector_counts.items():
                print(f"{sector}: {count} 只")
        
        print(f"\n前10只股票:")
        for i, stock in enumerate(results[:10]):
            sector_info = f" ({stock.get('sector', '未知板块')})" if 'sector' in stock else ""
            print(f"{i+1}. {stock['code']} {stock['name']}{sector_info} - "
                  f"价格: {stock['price']:.2f}, 涨跌幅: {stock['change_pct']:.2f}%")
    
    def calculate_stock_score(self, df, macd, boll_mid, boll_upper):
        """计算股票评分"""
        score = 0
        
        # 基础技术指标评分
        if df['change_pct'].iloc[-1] > 0:
            score += 1
        if macd.iloc[-1] > 0:
            score += 1
        
        # 成交量评分
        volume_ratio = df['volume'].iloc[-1] / df['volume'].rolling(20).mean().iloc[-1]
        if 0.5 <= volume_ratio <= 1.5:  # 适中的成交量
            score += 1
        
        # 布林带位置评分
        current_price = df['close'].iloc[-1]
        mid = boll_mid.iloc[-1]
        upper = boll_upper.iloc[-1]
        band_width = upper - mid
        distance_to_mid = abs(current_price - mid)
        
        if distance_to_mid <= band_width * 0.3:  # 接近中轨
            score += 1
        
        # 价格趋势评分
        if df['close'].iloc[-1] > df['close'].iloc[-5]:  # 5天内有上涨
            score += 1
        
        return score

    def get_ths_hot_concepts(self):
        """获取同花顺热门概念板块"""
        print("正在获取同花顺热门概念板块...")
        
        try:
            # 获取同花顺概念板块列表
            ths_concepts = ak.stock_board_concept_name_ths()
            print(f"同花顺数据结构: {ths_concepts.columns.tolist()}")
            print(f"数据形状: {ths_concepts.shape}")
            
            # 显示前几行数据以了解结构
            print("\n前5行数据:")
            print(ths_concepts.head())
            
            # 尝试找到概念名称列
            possible_name_columns = ['概念名称', 'name', '名称', 'concept_name', 'board_name', '板块名称']
            name_column = None
            
            for col in possible_name_columns:
                if col in ths_concepts.columns:
                    name_column = col
                    break
            
            if name_column:
                print(f"\n使用列名: {name_column}")
                concept_list = ths_concepts[name_column].tolist()
                
                print(f"\n=== 同花顺热门概念板块 (共{len(concept_list)}个) ===")
                for i, name in enumerate(concept_list[:50], 1):  # 显示前50个
                    print(f"{i:2d}. {name}")
                
                # 如果有涨跌幅等信息，也显示出来
                if '涨跌幅' in ths_concepts.columns:
                    print(f"\n=== 涨幅前10的概念板块 ===")
                    top_gainers = ths_concepts.nlargest(10, '涨跌幅')
                    for i, (_, row) in enumerate(top_gainers.iterrows(), 1):
                        print(f"{i:2d}. {row[name_column]} - 涨幅: {row['涨跌幅']:.2f}%")
                
                print("\n提示：获取成分股时建议使用以下概念名称进行测试：")
                print("- 人工智能")
                print("- 新能源汽车") 
                print("- 锂电池")
                print("- 光伏")
                
                return concept_list
            else:
                print("未找到概念名称列")
                print("可用列名:", ths_concepts.columns.tolist())
                return []
                
        except Exception as e:
            print(f"获取同花顺概念板块失败: {e}")
            return []
    
    def get_ths_concept_stocks(self, concept_name):
        """获取同花顺概念板块成分股"""
        try:
            print(f"获取同花顺概念 '{concept_name}' 的成分股...")
            
            # 尝试不同的akshare函数
            try:
                # 方法1：尝试东方财富概念成分股
                concept_stocks = ak.stock_board_concept_cons_em(symbol=concept_name)
                if not concept_stocks.empty:
                    print(f"东方财富数据结构: {concept_stocks.columns.tolist()}")
                    print(f"找到 {len(concept_stocks)} 只股票")
                    stocks = concept_stocks[['代码', '名称']].rename(columns={'代码': 'code', '名称': 'name'})
                    return stocks.to_dict('records')
            except Exception as e1:
                print(f"东方财富概念成分股查询失败: {e1}")
            
            # 方法2：尝试同花顺行业成分股（如果有的话）
            try:
                concept_stocks = ak.stock_board_cons_ths(symbol=concept_name)
                if not concept_stocks.empty:
                    print(f"同花顺数据结构: {concept_stocks.columns.tolist()}")
                    print(f"找到 {len(concept_stocks)} 只股票")
                    
                    # 尝试找到代码和名称列
                    code_col = None
                    name_col = None
                    
                    for col in ['代码', 'code', '股票代码', 'symbol']:
                        if col in concept_stocks.columns:
                            code_col = col
                            break
                    
                    for col in ['名称', 'name', '股票名称', 'stock_name']:
                        if col in concept_stocks.columns:
                            name_col = col
                            break
                    
                    if code_col and name_col:
                        stocks = concept_stocks[[code_col, name_col]].rename(
                            columns={code_col: 'code', name_col: 'name'}
                        )
                        return stocks.to_dict('records')
                    else:
                        print("未找到代码或名称列")
                        print("可用列名:", concept_stocks.columns.tolist())
                        return []
            except Exception as e2:
                print(f"同花顺成分股查询失败: {e2}")
            
            # 方法3：使用离线数据或提示用户
            print(f"无法获取 '{concept_name}' 的成分股数据")
            print("建议：")
            print("1. 检查概念名称是否正确")
            print("2. 尝试使用东方财富的概念名称")
            print("3. 使用模式4的板块扫描功能")
            return []
                
        except Exception as e:
            print(f"获取概念成分股时发生错误: {e}")
            return []

    def get_sector_stocks_offline(self, sector_name):
        """获取离线板块数据"""
        print(f"使用离线数据获取板块 '{sector_name}' 成分股...")
        
        # 离线板块数据字典
        offline_sectors = {
            "新能源": [
                {"code": "300750", "name": "宁德时代"},
                {"code": "002594", "name": "比亚迪"},
                {"code": "300274", "name": "阳光电源"},
                {"code": "002460", "name": "赣锋锂业"},
                {"code": "300014", "name": "亿纬锂能"},
                {"code": "688599", "name": "天合光能"},
                {"code": "300763", "name": "锦浪科技"},
                {"code": "002709", "name": "天赐材料"},
                {"code": "300438", "name": "鹏辉能源"},
                {"code": "300207", "name": "欣旺达"}
            ],
            "锂电池": [
                {"code": "300750", "name": "宁德时代"},
                {"code": "002594", "name": "比亚迪"},
                {"code": "002460", "name": "赣锋锂业"},
                {"code": "300014", "name": "亿纬锂能"},
                {"code": "002709", "name": "天赐材料"},
                {"code": "300438", "name": "鹏辉能源"},
                {"code": "300207", "name": "欣旺达"},
                {"code": "002812", "name": "恩捷股份"},
                {"code": "300073", "name": "当升科技"},
                {"code": "300068", "name": "南都电源"}
            ],
            "光伏": [
                {"code": "300274", "name": "阳光电源"},
                {"code": "688599", "name": "天合光能"},
                {"code": "300763", "name": "锦浪科技"},
                {"code": "002459", "name": "晶澳科技"},
                {"code": "300393", "name": "中来股份"},
                {"code": "300316", "name": "晶盛机电"},
                {"code": "002129", "name": "中环股份"},
                {"code": "300118", "name": "东方日升"},
                {"code": "300724", "name": "捷佳伟创"},
                {"code": "002506", "name": "协鑫集成"}
            ],
            "人工智能": [
                {"code": "000063", "name": "中兴通讯"},
                {"code": "002415", "name": "海康威视"},
                {"code": "300059", "name": "东方财富"},
                {"code": "002230", "name": "科大讯飞"},
                {"code": "300496", "name": "中科创达"},
                {"code": "002241", "name": "歌尔股份"},
                {"code": "300454", "name": "深信服"},
                {"code": "002353", "name": "杰瑞股份"},
                {"code": "300253", "name": "卫宁健康"},
                {"code": "300033", "name": "同花顺"}
            ],
            "半导体": [
                {"code": "000725", "name": "京东方A"},
                {"code": "002415", "name": "海康威视"},
                {"code": "300782", "name": "卓胜微"},
                {"code": "300661", "name": "圣邦股份"},
                {"code": "300408", "name": "三环集团"},
                {"code": "002049", "name": "紫光国微"},
                {"code": "300223", "name": "北京君正"},
                {"code": "300327", "name": "中颖电子"},
                {"code": "300373", "name": "扬杰科技"},
                {"code": "300474", "name": "景嘉微"}
            ],
            "医药": [
                {"code": "000858", "name": "五粮液"},
                {"code": "300015", "name": "爱尔眼科"},
                {"code": "000661", "name": "长春高新"},
                {"code": "300760", "name": "迈瑞医疗"},
                {"code": "002821", "name": "凯莱英"},
                {"code": "300347", "name": "泰格医药"},
                {"code": "300122", "name": "智飞生物"},
                {"code": "300601", "name": "康泰生物"},
                {"code": "300529", "name": "健帆生物"},
                {"code": "300003", "name": "乐普医疗"}
            ],
            "白酒": [
                {"code": "000858", "name": "五粮液"},
                {"code": "600519", "name": "贵州茅台"},
                {"code": "000596", "name": "古井贡酒"},
                {"code": "002304", "name": "洋河股份"},
                {"code": "000568", "name": "泸州老窖"},
                {"code": "600809", "name": "山西汾酒"},
                {"code": "000799", "name": "酒鬼酒"},
                {"code": "603369", "name": "今世缘"},
                {"code": "000860", "name": "顺鑫农业"},
                {"code": "600779", "name": "水井坊"}
            ]
        }
        
        # 查找匹配的板块
        if sector_name in offline_sectors:
            return offline_sectors[sector_name]
        
        # 模糊匹配
        for key, stocks in offline_sectors.items():
            if sector_name in key or key in sector_name:
                print(f"模糊匹配到板块: {key}")
                return stocks
        
        print(f"离线数据中未找到板块 '{sector_name}'")
        print(f"可用的离线板块: {list(offline_sectors.keys())}")
        return []

    def test_network_connection(self):
        """测试网络连接"""
        print("测试akshare网络连接...")
        try:
            # 测试获取股票列表
            stock_info = ak.stock_info_a_code_name()
            print(f"✓ 网络连接正常，获取到 {len(stock_info)} 只股票")
            
            # 测试获取概念板块
            concept_boards = ak.stock_board_concept_name_em()
            print(f"✓ 概念板块接口正常，获取到 {len(concept_boards)} 个概念")
            
            # 测试获取行业板块
            industry_boards = ak.stock_board_industry_name_em()
            print(f"✓ 行业板块接口正常，获取到 {len(industry_boards)} 个行业")
            
            return True
        except Exception as e:
            print(f"✗ 网络连接测试失败: {e}")
            print("建议：")
            print("1. 检查网络连接")
            print("2. 稍后重试")
            print("3. 使用离线数据进行测试")
            return False

def main():
    screener = StockScreener()
    
    print("=== 基于MACDBollVolumeStrategy的全市场选股系统 ===")
    print("策略条件：")
    print("1. 20天内有MACD金叉")
    print("2. 金叉后有大阳线+高成交量")
    print("3. MACD保持向上趋势")
    print("4. 股价在布林带中轨与上轨之间")
    print("5. 当前MACD在信号线之上")
    print("6. 前7天内有股价位于上轨附近")
    print("7. 现在股价缩量下降至接近中轨")
    print("-" * 50)
    
    # 选择运行模式
    print("\n运行模式选择：")
    print("1 - 测试前100只股票")
    print("2 - 测试前500只股票") 
    print("3 - 全市场扫描")
    print("4 - 板块扫描")
    print("5 - 查看可用板块列表")
    print("6 - 显示同花顺热门概念板块")
    print("7 - 测试同花顺函数")
    print("8 - 测试网络连接")
    
    mode = input("请选择运行模式 (1-8): ")
    
    if mode == "1":
        results = screener.run_screening(max_stocks=100)
        screener.save_results(results)
    elif mode == "2":
        results = screener.run_screening(max_stocks=500)
        screener.save_results(results)
    elif mode == "3":
        results = screener.run_screening()
        screener.save_results(results)
    elif mode == "4":
        # 板块扫描模式
        print("\n请输入要扫描的板块名称（用逗号分隔）")
        print("例如: 新能源,锂电池,光伏")
        print("或者: 银行,房地产,医药")
        
        sector_input = input("板块名称: ")
        if sector_input.strip():
            sector_names = [name.strip() for name in sector_input.split(',')]
            results = screener.run_sector_screening(sector_names)
            
            # 保存结果时添加板块信息到文件名
            sector_suffix = "_".join(sector_names[:3])  # 最多取前3个板块名
            filename = f"sector_{sector_suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            screener.save_results(results, filename)
        else:
            print("未输入板块名称")
    elif mode == "5":
        # 查看可用板块
        screener.get_available_sectors()
        print("\n提示：复制板块名称用于模式4的板块扫描")
    elif mode == "6":
        # 显示同花顺热门概念板块
        concepts = screener.get_ths_hot_concepts()
        if concepts:
            print(f"\n获取到 {len(concepts)} 个概念板块")
            print("\n提示：可以选择感兴趣的概念板块用于模式4的板块扫描")
            
            # 可选：测试获取某个概念的成分股
            test_concept = input("\n输入概念名称测试获取成分股（回车跳过）: ")
            if test_concept.strip():
                stocks = screener.get_ths_concept_stocks_v2(test_concept.strip())
                if stocks:
                    print(f"\n'{test_concept}' 概念成分股前10只:")
                    for i, stock in enumerate(stocks[:10], 1):
                        print(f"{i:2d}. {stock['code']} {stock['name']}")
        else:
            print("未能获取同花顺概念板块数据")
    elif mode == "7":
        # 测试同花顺函数
        screener.test_ths_functions()
    elif mode == "8":
        # 测试网络连接
        screener.test_network_connection()
    else:
        print("无效的运行模式")
        return
    
    print("选股完成！")

if __name__ == "__main__":
    main()





